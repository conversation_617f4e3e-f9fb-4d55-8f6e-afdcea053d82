package model

// VideoPlaylistInfo 视频播放列表信息
type VideoPlaylistInfo struct {
	PlaylistId  uint32 `json:"playlist_id" dc:"播放列表ID"`
	Name        string `json:"name" dc:"播放列表名称（当前语言）"`
	ShortTitle  string `json:"short_title" dc:"播放列表短标题（当前语言）"`
	Description string `json:"description" dc:"播放列表描述（当前语言）"` // 专题封面图片链接
	CoverUrl    string `json:"cover_url" dc:"专题封面图片链接"`
	VideoCount  uint32 `json:"video_count" dc:"播放列表下视频数量"`
}

// VideoPlaylistListOutput 视频播放列表列表输出
type VideoPlaylistListOutput struct {
	List []*VideoPlaylistInfo `json:"list" dc:"播放列表列表"`
	Page *PageResponse        `json:"page" dc:"分页信息"`
}

// VideoListItem 视频列表项（用于列表展示的简化版本）
type VideoListItem struct {
	VideoId       uint32 `json:"video_id" dc:"视频ID"`
	CategoryId    uint32 `json:"category_id" dc:"分类ID"`
	Title         string `json:"title" dc:"视频标题（当前语言）"`
	VideoCoverUrl string `json:"video_cover_url" dc:"视频封面图片URL"`
	VideoDuration uint32 `json:"video_duration" dc:"视频时长(秒)"`
}

// VideoDetailInfo 视频详情信息（完整版本）
type VideoDetailInfo struct {
	VideoId          uint32 `json:"video_id" dc:"视频ID"`
	CategoryId       uint32 `json:"category_id" dc:"分类ID"`
	Title            string `json:"title" dc:"视频标题（当前语言）"`
	Description      string `json:"description" dc:"视频描述（当前语言）"`
	VideoCoverUrl    string `json:"video_cover_url" dc:"视频封面图片URL"`
	VideoUrl         string `json:"video_url" dc:"视频文件URL"`
	Author           string `json:"author" dc:"视频作者"`
	AuthorLogo       string `json:"author_logo" dc:"作者头像URL"`
	AuthorAuthStatus uint32 `json:"author_auth_status" dc:"作者认证状态：0-未认证，1-已认证"`
	PublishState     uint32 `json:"publish_state" dc:"发布状态：0-待发布，1-已发布，2-已下线"`
	IsCollected      bool   `json:"is_collected" dc:"当前用户是否已收藏（需要登录）"`
}

// PlaylistBasicInfo 播放列表基本信息（用于VideoList返回）
type PlaylistBasicInfo struct {
	PlaylistId uint32 `json:"playlist_id" dc:"播放列表ID"`
	Name       string `json:"name" dc:"播放列表名称（当前语言）"`
}

// VideoListInput 视频列表请求输入参数
type VideoListInput struct {
	CategoryId uint32 `json:"category_id" dc:"分类ID（可选）"`
	PlaylistId uint32 `json:"playlist_id" dc:"播放列表ID（可选）"`
	Title      string `json:"title" dc:"视频标题搜索（可选）"`
	SortBy     string `json:"sort_by" dc:"排序方式：view_count, created_at, published_at"`
	SortOrder  string `json:"sort_order" dc:"排序顺序：asc, desc"`
	LanguageId uint32 `json:"language_id" dc:"语言ID"`
	Page       int    `json:"page" dc:"页码"`
	Size       int    `json:"size" dc:"每页数量"`
}

// VideoListOutput 视频列表输出
type VideoListOutput struct {
	List     []*VideoListItem   `json:"list" dc:"视频列表"`
	Page     *PageResponse      `json:"page" dc:"分页信息"`
	Playlist *PlaylistBasicInfo `json:"playlist,omitempty" dc:"播放列表基本信息（当通过playlist_id查询时返回）"`
}

// VideoDetailOutput 视频详情输出
type VideoDetailOutput struct {
	Video *VideoDetailInfo `json:"video" dc:"视频详情"`
}

// RecommendedVideoListInput 推荐视频列表请求输入参数
type RecommendedVideoListInput struct {
	CategoryId uint32 `json:"category_id" dc:"分类ID（可选）"`
	LanguageId uint32 `json:"language_id" dc:"语言ID"`
	Page       int    `json:"page" dc:"页码"`
	Size       int    `json:"size" dc:"每页数量"`
}

// VideoCollectInput 视频收藏请求输入参数
type VideoCollectInput struct {
	UserId  uint64 `json:"user_id" dc:"用户ID"`
	VideoId uint32 `json:"video_id" dc:"视频ID"`
	IsAdd   uint32 `json:"is_add" dc:"是否添加收藏，1-添加，0-取消收藏"`
}
