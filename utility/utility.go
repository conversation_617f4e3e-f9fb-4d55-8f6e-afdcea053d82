package utility

import (
	"context"
	"net"
	"regexp"
	"strings"
	"time"
	"unicode"

	"github.com/gogf/gf/v2/text/gstr"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/grand"
	"github.com/shopspring/decimal"
)

func Max[S int8 | int16 | int | int32 | int64 | uint8 | uint16 | uint | uint32 | uint64](s1 S, s2 S) S {
	if s1 > s2 {
		return s1
	}
	return s2
}

func Min[S int8 | int16 | int | int32 | int64 | uint8 | uint16 | uint | uint32 | uint64](s1 S, s2 S) S {
	if s1 < s2 {
		return s1
	}
	return s2
}

func ToPtr[T any](v T) *T {
	return &v
}

// N 随机返回 t-m 到 t的范围
func N(t int, m int) int64 {
	return int64(grand.N(t-m, t))
}

// Map 将切片转map
func Map[T any, S comparable](data []T, f func(row T) S) map[S]T {
	res := make(map[S]T, len(data))
	if data == nil {
		return res
	}
	for i := 0; i < len(data); i++ {
		res[f(data[i])] = data[i]
	}
	return res
}

// Slice 将一个切片的结构体转成切片
func Slice[T any, S comparable](data []T, f func(row T) S) []S {
	res := make([]S, 0, len(data))
	if data == nil {
		return res
	}
	for i := 0; i < len(data); i++ {
		res = append(res, f(data[i]))
	}
	return res
}

// SliceByMap 将一个map转成切片
func SliceByMap[T any, S1 comparable, S2 comparable](data map[S1]T, f func(row T) S2) []S2 {
	res := make([]S2, 0, len(data))
	if data == nil {
		return res
	}
	for _, row := range data {
		res = append(res, f(row))
	}
	return res
}

// MapSlice 将切片转map[]slice
// T: 切片的元素类型,  S: 结果的key的类型
func MapSlice[T any, S comparable](data []T, f func(row T) S) map[S][]*T {
	res := make(map[S][]*T, len(data))
	if data == nil {
		return res
	}
	for i := 0; i < len(data); i++ {
		res[f(data[i])] = append(res[f(data[i])], &data[i])
	}
	return res
}

// FindIntersection 查找二个切片的交集
func FindIntersection[S comparable](arr1, arr2 []S) []S {
	small := arr1
	big := arr2
	if len(arr1) > len(arr2) {
		small = arr2
		big = arr1
	}

	m := make(map[S]bool, len(small))
	for _, v := range small {
		m[v] = true
	}

	s := make([]S, 0, len(small))
	for _, v := range big {
		if m[v] {
			s = append(s, v)
		}
	}
	return s
}

// 当前是否是线上生产环境
func IsProd(ctx context.Context) bool {
	gv, err := g.Cfg().Get(ctx, "server.env")
	return err == nil && gv.String() == "prod"
}

// 获取一天的周一所在日期
func GetMondayOfWeek(input *gtime.Time) *gtime.Time {
	// 获取当前时间所在的星期一
	weekday := input.Weekday()

	// 计算距离周一的天数差
	daysUntilMonday := int(time.Monday - weekday)
	if daysUntilMonday > 0 {
		daysUntilMonday -= 7
	}

	// 计算周一的日期
	monday := input.AddDate(0, 0, daysUntilMonday)
	return monday
}

// 获取下周一的0点0分0秒
func GetNextMonday(t time.Time) time.Time {
	weekday := int(t.Weekday())              // 计算当前时间是周几
	daysUntilNextMonday := (8 - weekday) % 7 // 计算距离下周一的天数差
	if daysUntilNextMonday == 0 {
		daysUntilNextMonday = 7
	}
	// 计算下周一的时间
	nextMonday := t.AddDate(0, 0, daysUntilNextMonday)
	// 设置时间为0点0分0秒
	return time.Date(nextMonday.Year(), nextMonday.Month(), nextMonday.Day(), 0, 0, 0, 0, nextMonday.Location())
}

// If 三目运算
func If[T any](condition bool, trueVal T, falseVal T) (rs T) {
	if condition {
		rs = trueVal
	} else {
		rs = falseVal
	}
	return
}

// 是否存在于数组中
func In[K comparable](s K, ss []K) bool {
	for _, v := range ss {
		if v == s {
			return true
		}
	}
	return false
}

func IntBool(b bool) int {
	if b {
		return 1
	}
	return 2
}

var DecimalHundred = decimal.NewFromInt(100)
var DecimalOne = decimal.NewFromInt(1)

// GetShowRate 获取用于显示的比例
func GetShowRate(rate decimal.Decimal) decimal.Decimal {
	return rate.Mul(DecimalHundred)
}

// GetDBRate 获取用于存储的比例
func GetDBRate(rate decimal.Decimal) decimal.Decimal {
	return rate.Truncate(2).Div(decimal.NewFromInt(100))
}

// IsLocal 当前是否是线上生产环境
func IsLocal(ctx context.Context) bool {
	gv, err := g.Cfg().Get(ctx, "server.env")
	return err == nil && gv.String() == "local"
}

// ContainsChinese 判断是否包含中文
func ContainsChinese(s string) bool {
	for _, r := range s {
		if unicode.Is(unicode.Han, r) {
			return true
		}
	}
	return false
}

func ContainsSpecialChars(srcObjectName string) bool {
	// 正则表达式匹配 []、{}、() 等特殊字符
	pattern := `[\\[\]{}()<>|^$+*]`
	re := regexp.MustCompile(pattern)
	return re.MatchString(srcObjectName)
}

// 脱敏。   abcdef => a****f
func TuoMin(s string) string {
	if len(s) < 3 {
		return "****"
	}
	ss := make([]byte, 7)
	copy(ss[:2], s[0:2])
	ss[6] = s[len(s)-1]
	for i := 2; i < 6; i++ {
		ss[i] = '*'
	}
	return string(ss)
}

// 解析 https://m1.ngtests.com?agentCode=z8xgzx
func ParseDomain(domain string) string {
	if !gstr.Contains(domain, "?") {
		return domain
	}

	arrs := gstr.Split(domain, "?")
	return arrs[0]
}

func GetLocalLANIP() string {
	interfaces, err := net.Interfaces()
	if err != nil {
		return "0.0.0.0"
	}

	for _, iface := range interfaces {
		addrs, err := iface.Addrs()
		if err != nil {
			continue
		}
		for _, addr := range addrs {
			// 只要 IPv4
			if ipNet, ok := addr.(*net.IPNet); ok && !ipNet.IP.IsLoopback() {
				ipv4 := ipNet.IP.To4()
				if ipv4 == nil {
					continue
				}
				if ipv4 != nil && (strings.HasPrefix(ipv4.String(), "192.168.") ||
					strings.HasPrefix(ipv4.String(), "10.") ||
					strings.HasPrefix(ipv4.String(), "172.")) {
					g.Log().Info(nil, iface.Name, ipv4)
					return ipv4.String()
				}
			}
		}
	}
	return "0.0.0.0"
}

// Desensitize 中间脱敏，仅保留首尾各两位
func Desensitize(s string) string {
	runeStr := []rune(s)
	length := len(runeStr)

	if length <= 4 {
		// 长度小于等于4时，不处理或根据需求处理
		return s
	}

	// 构造脱敏字符串
	start := string(runeStr[:2])
	end := string(runeStr[length-2:])
	masked := strings.Repeat("*", length-4)

	return start + masked + end
}
