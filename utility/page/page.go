package page

import (
	"halalplus/api/common"
)

// PageConfig 分页配置
type PageConfig struct {
	DefaultPage int // 默认页码
	DefaultSize int // 默认页大小
	MaxSize     int // 最大页大小
}

// DefaultPageConfig 默认分页配置
var DefaultPageConfig = &PageConfig{
	DefaultPage: 1,
	DefaultSize: 10,
	MaxSize:     100,
}

// PageParams 标准化的分页参数
type PageParams struct {
	Page int // 页码
	Size int // 页大小
}

type PageResponse struct {
	Page  int `json:"page" dc:"当前页"`
	Size  int `json:"size" dc:"每页数量"`
	Total int `json:"total" dc:"总数"`
}

// NormalizePageRequest 标准化protobuf的PageRequest
// 将protobuf的PageRequest转换为标准的PageParams，并应用默认值和限制
func NormalizePageRequest(req *common.PageRequest) *PageParams {
	return NormalizePageRequestWithConfig(req, DefaultPageConfig)
}

// NormalizePageRequestWithConfig 使用自定义配置标准化PageRequest
func NormalizePageRequestWithConfig(req *common.PageRequest, config *PageConfig) *PageParams {
	if req == nil {
		return &PageParams{
			Page: config.DefaultPage,
			Size: config.DefaultSize,
		}
	}

	page := int(req.Page)
	size := int(req.Size)

	// 应用默认值
	if page <= 0 {
		page = config.DefaultPage
	}
	if size <= 0 {
		size = config.DefaultSize
	}

	// 应用最大限制
	if size > config.MaxSize {
		size = config.MaxSize
	}

	return &PageParams{
		Page: page,
		Size: size,
	}
}

// ToPageResponse 将PageParams转换为protobuf的PageResponse
func (p *PageParams) ToPageResponse(total int) *common.PageResponse {
	return &common.PageResponse{
		Page:  int32(p.Page),
		Size:  int32(p.Size),
		Total: int32(total),
	}
}
